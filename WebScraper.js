import dotenv from 'dotenv';
import { MAX_RETRY_COUNT, RETRY_DELAY } from './config.js';

// Import all the function modules
import {
  closeBrowser,
  getBrowser,
  getPage,
  initBrowser,
} from './browser-ops/BrowserManager.js';
import { navigateToUrl } from './browser-ops/PageNavigator.js';
import infiniteScrollUntilNoMoreNewNetworkRequest from './browser-ops/PageScroller.js';
import {
  convertAndImproveMarkdownFromHTML,
  convertAndImproveMarkdownFromMarkdown,
} from './core/AIMarkdownConvertor.js';
import { combineContent } from './core/ContentCombiner.js';
import {
  findInteractiveElements,
  processInteractiveElementsContent,
} from './core/InteractiveElementProcessor.js';
import {
  convertToMarkdown,
  fixAndFormatHTML,
} from './core/NormalMarkdownConvertor.js';
import { saveToFile } from './utils/FileManager.js';
import {
  ERROR_CATEGORIES,
  getRetryConfig,
  handleError,
} from './utils/GlobalErrorHandler.js';
import { SimplePerformanceMonitor } from './utils/SimplePerformanceMonitor.js';
import { createStreamingService } from './utils/StreamingService.js';

dotenv.config({
  quiet: true
});

// Scraping modes constants
export const SCRAPING_MODES = {
  NORMAL: 'normal',
  BEAST: 'beast',
};

/**
 * Create scraping context with shared state and utilities
 * @returns {Object} Scraping context with shared utilities
 */
function createScrapingContext() {
  return {
    currentMode: SCRAPING_MODES.BEAST,
    browser: null,
    page: null,
    performanceMonitor: new SimplePerformanceMonitor(),
  };
}

/**
 * Handle iframes by extracting their content and replacing them in the main page
 * This ensures iframe content is included in the final extracted content
 * @param {Object} page - Playwright page object
 */
async function handleIframes(page) {
  const frames = page.frames();

  for (const frame of frames) {
    if (frame === page.mainFrame()) continue;

    try {
      const iframeElement = await frame.frameElement();
      if (!iframeElement) continue;

      // Get iframe source information
      const iframeSrc = await iframeElement.evaluate(
        iframe => iframe.src || iframe.getAttribute('srcdoc') || 'inline'
      );

      try {
        await frame.waitForLoadState('domcontentloaded', { timeout: 5000 });
      } catch (timeoutError) {
        console.warn(`Iframe load timeout for: ${iframeSrc}, proceeding anyway...`, timeoutError.message);
      }

      // Extract raw HTML content from iframe without any filtering
      const frameContent = await frame.evaluate(() => {
        // Return the complete raw HTML content
        return document.documentElement.outerHTML;
      }, {});

      // Only replace if we got meaningful content
      if (frameContent && frameContent.trim().length > 0) {
        // Replace iframe with extracted content
        await iframeElement.evaluate(
          (iframe, config) => {
            const container = document.createElement('div');
            container.className = 'iframe-content-replacement';
            container.setAttribute(
              'data-original-iframe-src',
              config.iframeSrc || ''
            );
            container.innerHTML = config.content;
            iframe.parentNode.replaceChild(container, iframe);
          },
          { content: frameContent, iframeSrc }
        );
      } else {
        console.log(`No content found in iframe: ${iframeSrc}`);
      }
    } catch (e) {
      const frameUrl = frame.url();
      console.log(`❌ Cannot access iframe (${frameUrl}): ${e.message}`);

      // Log specific error types for better debugging
      if (e.message.includes('cross-origin')) {
        console.log('   → Cross-origin restriction detected');
      } else if (e.message.includes('timeout')) {
        console.log('   → Frame loading timeout');
      }
    }
  }
}

/**
 * Process scraping in normal mode (simple extraction)
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 */
async function processNormalMode(context, outputHtmlFilename, userQuery) {
  context.performanceMonitor.startPhase('content-extraction');

  // after processing, remove junk from html like script tags, style tags, etc.
  await context.page.evaluate(() => {
    document
      .querySelectorAll("style, link, script[src^='https://']")
      .forEach(element => element.remove());
  });

  const rawHTML = await context.page.content();
  await closeBrowser();

  const cleanedHTML = await fixAndFormatHTML(rawHTML);
  await saveToFile(cleanedHTML, `${outputHtmlFilename}.html`);
  const rawMarkdown = await convertToMarkdown(cleanedHTML);

  if (userQuery) {
    const improvedMarkdown = await convertAndImproveMarkdownFromMarkdown(
      rawMarkdown,
      userQuery
    );
    await saveToFile(improvedMarkdown, `${outputHtmlFilename}.md`);
  } else {
    await saveToFile(rawMarkdown, `${outputHtmlFilename}.raw.md`);
  }
  context.performanceMonitor.endPhase();
  return { enhancedError: null };
}

/**
 * Process scraping in beast mode (advanced extraction with interactive elements)
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 * @param {string} userQuery - Optional user query for focused content extraction
 * @returns {Object} Processing result with enhancedError if any
 */
async function processBeastMode(context, outputHtmlFilename, userQuery) {
  let enhancedError = null;

  // Step 3: Find interactive elements that might reveal hidden content
  context.performanceMonitor.startPhase('AI element detection');

  // for AI analysis, clone html content without all script tags -> this is important for the AI to work properly
  const clonedHTML = await context.page.evaluate(() => {
    const clonedPage = document.cloneNode(true);
    clonedPage
      .querySelectorAll('script, link')
      .forEach(element => element.remove());

    // remove all attributes except for table and its children attributes, iframe content or code/pre tags & it's children
    clonedPage.querySelectorAll('*').forEach(element => {
      // Don't remove from iframe content or code/pre tags & it's children
      if (
        element.tagName === 'IFRAME' ||
        element.tagName === 'CODE' ||
        element.tagName === 'PRE'
      ) {
        return;
      }

      if (element.tagName !== 'TABLE' && !element.closest('table')) {
        Array.from(element.attributes).forEach(attr => {
          if (
            attr.name !== 'class' &&
            attr.name !== 'id' &&
            !attr.name.startsWith('data-') &&
            !attr.name.startsWith('aria-')
          ) {
            element.removeAttribute(attr.name);
          }
        });
      }
    });
    // return html content
    return clonedPage.documentElement.outerHTML;
  });

  // greater than 500K characters, then we will skip AI analysis and will do normal mode
  if (clonedHTML.length > 500000) {
    console.log(
      `🔄 Skipping AI analysis for large HTML content, HTML length: ${clonedHTML.length} characters`
    );
    await processNormalMode(context, outputHtmlFilename, userQuery);
    context.performanceMonitor.endPhase();
  } else {
    let interactiveElements = { elements: [] };
    try {
      interactiveElements = await findInteractiveElements(
        clonedHTML,
        userQuery
      );
    } catch (error) {
      console.error('Failed to find interactive elements:', error.message);
      console.error('Error details:', error.stack?.split('\n').slice(0, 3).join('\n'));

      const handledError = await handleError(error, {
        operation: 'findInteractiveElements',
        url: context.page?.url?.() || 'unknown',
        userQuery,
      });

      if (!handledError.shouldRetry) {
        enhancedError = handledError;
      } else {
        throw handledError; // Allow retry for retryable errors
      }
    }
    context.performanceMonitor.endPhase();
    console.log('------------------------------------------------------');

    // Step 4: Process interactive elements and extract dynamic content
    console.log('------------------------------------------------------');
    context.performanceMonitor.startPhase('interactive processing');
    let dynamicContents = [];
    if (interactiveElements.elements.length > 0) {
      try {
        dynamicContents = await processInteractiveElementsContent(
          context.page,
          interactiveElements
        );
      } catch (error) {
        console.error('Failed to process interactive elements:', error.message);
        console.error('Error details:', error.stack?.split('\n').slice(0, 3).join('\n'));

        const handledError = await handleError(error, {
          operation: 'processInteractiveElementsContent',
          url: context.page?.url?.() || 'unknown',
          elementsCount: interactiveElements.elements.length,
          userQuery,
        });

        if (!handledError.shouldRetry) {
          console.log('🔄 Continuing without dynamic content extraction...');
          enhancedError = handledError;
        } else {
          throw handledError; // Allow retry for retryable errors
        }
      }
    }
    context.performanceMonitor.endPhase();
    console.log('------------------------------------------------------');

    // Step 5: Combine main content with dynamic content
    console.log('------------------------------------------------------');
    context.performanceMonitor.startPhase('content processing');

    const combinedHtml = await combineContent(context.page, dynamicContents);
    await closeBrowser();
    const cleanedHTML = await fixAndFormatHTML(combinedHtml);
    await saveToFile(cleanedHTML, `${outputHtmlFilename}.html`);

    // Step 6: Convert to markdown and improve with AI
    if (userQuery) {
      const improvedMarkdown = await convertAndImproveMarkdownFromHTML(
        cleanedHTML,
        userQuery,
        null // No streaming service in regular mode
      );
      await saveToFile(improvedMarkdown, `${outputHtmlFilename}.md`);
    } else {
      const rawMarkdown = await convertToMarkdown(cleanedHTML);
      await saveToFile(rawMarkdown, `${outputHtmlFilename}.raw.md`);
    }
  }

  context.performanceMonitor.endPhase();
  return { enhancedError };
}

/**
 * Main scraper function that orchestrates the entire process
 * @param {string} url - The URL to scrape
 * @param {string} outputHtmlFilename - The filename to save the result as (default: "scraped")
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @returns {Promise<boolean>} - Success status
 */
export async function scrape(
  url,
  outputHtmlFilename = 'scraped',
  userQuery = '',
  mode = SCRAPING_MODES.AUTO
) {
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;

  while (retryCount < MAX_RETRY_COUNT && !success) {
    try {
      console.log(`Scraping ${url} (attempt ${retryCount + 1})`);

      // Create scraping context
      context = createScrapingContext();
      context.currentMode = mode;

      // Start performance monitoring
      context.performanceMonitor.start();

      // Step 1: Initialize browser and get page
      context.performanceMonitor.startPhase('browser setup');
      console.log('------------------------------------------------------');
      const browserInitialized = await initBrowser();
      if (!browserInitialized) {
        throw new Error('Failed to initialize browser');
      }

      context.browser = getBrowser();
      context.page = getPage();

      // Set browser context for enhanced monitoring
      context.performanceMonitor.setBrowserContext(context.page);

      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      // Step 2: Navigate to URL and wait for page to stabilize
      console.log('------------------------------------------------------');
      context.performanceMonitor.startPhase('page loading');
      await navigateToUrl(context.page, url);
      await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      // Step 3: Process iframes by extracting their content
      console.log('------------------------------------------------------');
      context.performanceMonitor.startPhase('iframe processing');
      await handleIframes(context.page);
      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      if (context.currentMode === SCRAPING_MODES.NORMAL) {
        await processNormalMode(context, outputHtmlFilename);
      } else {
        const result = await processBeastMode(
          context,
          outputHtmlFilename,
          userQuery
        );
        enhancedError = result.enhancedError;
      }

      success = true;

      // Stop performance monitoring
      context.performanceMonitor.stop();

      // Show final status
      if (enhancedError) {
        console.log('🔄 Scraping completed with limitations:');
        console.log('   ✅ HTML extraction: Success');
        console.log(`   ⚠️ Enhanced features: ${enhancedError.userMessage}`);
        console.log(`   🔍 Error ID: ${enhancedError.stackId}`);
      } else {
        console.log('✅ Scraping completed successfully with all features!');
      }
    } catch (error) {
      // Handle the error with our global error handler
      const handledError = await handleError(error, {
        operation: 'scrape',
        url,
        attempt: retryCount + 1,
        mode: context?.currentMode || mode,
        outputFile: outputHtmlFilename,
      });

      // Check if this error should stop retries
      if (
        !handledError.shouldRetry ||
        handledError.category === ERROR_CATEGORIES.AUTH ||
        handledError.category === ERROR_CATEGORIES.RATE_LIMIT
      ) {
        console.log(
          `\n🛑 Scraping stopped - Error ID: ${handledError.stackId}`
        );
        break; // Don't retry for auth, rate limit, or non-retryable errors
      }

      retryCount++;

      if (retryCount < MAX_RETRY_COUNT) {
        const retryConfig = getRetryConfig(handledError);
        const delay =
          retryConfig.strategy === 'exponential'
            ? RETRY_DELAY * Math.pow(2, retryCount - 1)
            : RETRY_DELAY;

        console.log(
          `🔄 Retrying in ${delay}ms... (attempt ${
            retryCount + 1
          }/${MAX_RETRY_COUNT})`
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        console.log(
          `\n❌ Maximum retry attempts (${MAX_RETRY_COUNT}) reached - Error ID: ${handledError.stackId}`
        );
      }
    } finally {
      // Always close browser in finally block
      if (context && context.browser) {
        await closeBrowser();
      }

      // Stop performance monitoring if it was started
      if (
        context &&
        context.performanceMonitor &&
        context.performanceMonitor.monitoringInterval
      ) {
        context.performanceMonitor.stop();
      }
    }
  }

  return success;
}

/**
 * Streaming-enabled scraper function
 * @param {string} url - The URL to scrape
 * @param {string} outputHtmlFilename - The filename identifier (default: "scraped")
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @param {Function} progressCallback - Optional callback for streaming progress updates
 * @returns {Promise<Object>} - Structured result with markdown and HTML content
 */
export async function scrapeWithStreaming(
  url,
  outputHtmlFilename = 'scraped',
  userQuery = '',
  mode = SCRAPING_MODES.BEAST,
  progressCallback = null
) {
  const startTime = Date.now();
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;
  let finalMarkdown = null;
  let finalHtml = null;

  // Initialize streaming service
  const streamingService = createStreamingService(progressCallback);

  try {
    streamingService.log(`Starting scraping process for ${url}`, 'info');

    while (retryCount < MAX_RETRY_COUNT && !success) {
      try {
        streamingService.log(
          `Scraping ${url} (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
          'info'
        );

        // Create scraping context
        context = createScrapingContext();
        context.currentMode = mode;
        context.streamingService = streamingService;

        // Start performance monitoring
        context.performanceMonitor.start();

        // Step 1: Initialize browser and get page
        streamingService.startPhase(
          'browser-setup',
          'Initializing browser and setting up page'
        );
        context.performanceMonitor.startPhase('browser setup');

        const browserInitialized = await initBrowser();
        if (!browserInitialized) {
          throw new Error('Failed to initialize browser');
        }

        context.browser = getBrowser();
        context.page = getPage();

        // Set browser context for enhanced monitoring
        context.performanceMonitor.setBrowserContext(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true, browser: 'initialized' });

        // Step 2: Navigate to URL and wait for page to stabilize
        streamingService.startPhase(
          'page-loading',
          `Navigating to ${url} and waiting for page to load`
        );
        context.performanceMonitor.startPhase('page loading');

        await navigateToUrl(context.page, url);
        streamingService.updateProgress({
          message:
            'Page loaded, performing infinite scroll to load dynamic content',
        });

        await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true, url: url });

        // Step 3: Process iframes by extracting their content
        streamingService.startPhase(
          'iframe-processing',
          'Processing iframe content'
        );
        context.performanceMonitor.startPhase('iframe processing');

        await handleIframes(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true });

        // Step 4: Execute scraping based on mode
        let scrapingResult;
        if (context.currentMode === SCRAPING_MODES.NORMAL) {
          scrapingResult = await processNormalModeWithStreaming(
            context,
            outputHtmlFilename,
            userQuery
          );
        } else {
          scrapingResult = await processBeastModeWithStreaming(
            context,
            outputHtmlFilename,
            userQuery
          );
        }

        enhancedError = scrapingResult.enhancedError;
        finalMarkdown = scrapingResult.markdown;
        finalHtml = scrapingResult.html;

        success = true;

        // Stop performance monitoring
        context.performanceMonitor.stop();

        // Log final status
        if (enhancedError) {
          streamingService.log('Scraping completed with limitations', 'warn');
          streamingService.log(
            `Enhanced features: ${enhancedError.userMessage}`,
            'warn'
          );
        } else {
          streamingService.log(
            'Scraping completed successfully with all features!',
            'info'
          );
        }
      } catch (error) {
        // Stream the error
        streamingService.streamError(error, {
          url,
          attempt: retryCount + 1,
          mode: context?.currentMode || mode,
        });

        // Handle the error with our global error handler
        const handledError = await handleError(error, {
          operation: 'scrapeWithStreaming',
          url,
          attempt: retryCount + 1,
          mode: context?.currentMode || mode,
          outputFile: outputHtmlFilename,
        });

        // Check if this error should stop retries
        if (
          !handledError.shouldRetry ||
          handledError.category === ERROR_CATEGORIES.AUTH ||
          handledError.category === ERROR_CATEGORIES.RATE_LIMIT
        ) {
          streamingService.log(
            `Scraping stopped - Error ID: ${handledError.stackId}`,
            'error'
          );
          break;
        }

        retryCount++;

        if (retryCount < MAX_RETRY_COUNT) {
          const retryConfig = getRetryConfig(handledError);
          const delay =
            retryConfig.strategy === 'exponential'
              ? RETRY_DELAY * Math.pow(2, retryCount - 1)
              : RETRY_DELAY;

          streamingService.log(
            `Retrying in ${delay}ms... (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
            'info'
          );
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          streamingService.log(
            `Maximum retry attempts (${MAX_RETRY_COUNT}) reached - Error ID: ${handledError.stackId}`,
            'error'
          );
        }
      } finally {
        // Always close browser in finally block
        if (context && context.browser) {
          await closeBrowser();
        }

        // Stop performance monitoring if it was started
        if (
          context &&
          context.performanceMonitor &&
          context.performanceMonitor.monitoringInterval
        ) {
          context.performanceMonitor.stop();
        }
      }
    }

    // Complete the streaming process
    const processingTime = Date.now() - startTime;
    const result = {
      success,
      markdown: finalMarkdown,
      html: finalHtml,
      processingTime,
      enhancedError,
    };

    streamingService.complete(result);
    return result;
  } catch (error) {
    streamingService.streamError(error);
    streamingService.log('Fatal error in scraping process', 'error');

    return {
      success: false,
      markdown: null,
      html: null,
      processingTime: Date.now() - startTime,
      error: error.message,
      enhancedError: null,
    };
  } finally {
    // Cleanup streaming service
    streamingService.cleanup();
  }
}

/**
 * Process scraping in normal mode with streaming support
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 * @param {string} userQuery - User query
 */
async function processNormalModeWithStreaming(
  context,
  _outputHtmlFilename,
  userQuery
) {
  const streamingService = context.streamingService;

  streamingService.startPhase(
    'content-extraction',
    'Extracting and processing content in normal mode'
  );
  context.performanceMonitor.startPhase('content-extraction');

  // Clean up the page content
  await context.page.evaluate(() => {
    document
      .querySelectorAll("style, link, script[src^='https://']")
      .forEach(element => element.remove());
  });

  streamingService.updateProgress({
    message: 'Cleaning up page content and extracting HTML',
  });
  const rawHTML = await context.page.content();
  await closeBrowser();

  streamingService.updateProgress({ message: 'Converting HTML to markdown' });
  const cleanedHTML = await fixAndFormatHTML(rawHTML);
  const rawMarkdown = await convertToMarkdown(cleanedHTML);

  let finalMarkdown = rawMarkdown;

  if (userQuery) {
    streamingService.updateProgress({
      message: 'Improving markdown with AI based on user query',
    });
    finalMarkdown = await convertAndImproveMarkdownFromMarkdown(
      rawMarkdown,
      userQuery
    );
  }

  context.performanceMonitor.endPhase();
  streamingService.endPhase({
    success: true,
    markdownLength: finalMarkdown?.length || 0,
    htmlLength: cleanedHTML?.length || 0,
  });

  return {
    enhancedError: null,
    markdown: finalMarkdown,
    html: cleanedHTML,
  };
}

/**
 * Process scraping in beast mode with streaming support
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 * @param {string} userQuery - User query
 */
async function processBeastModeWithStreaming(
  context,
  _outputHtmlFilename,
  userQuery
) {
  const streamingService = context.streamingService;
  let enhancedError = null;
  let dynamicContents = { contents: [] };

  // Step 1: Find interactive elements that might reveal hidden content
  streamingService.startPhase(
    'ai-element-detection',
    'Using AI to detect interactive elements'
  );
  context.performanceMonitor.startPhase('AI element detection');

  // Clone HTML content without script tags for AI analysis
  streamingService.log('📄 Starting HTML cloning process...', 'info');

  let clonedHTML;
  try {
    clonedHTML = await context.page.evaluate(() => {
      try {
        console.log('🌐 Browser: Starting document cloning...');

        // Use document.documentElement instead of document.cloneNode(true)
        // because document.cloneNode(true) doesn't include the HTML element itself
        const sourceElement = document.documentElement || document.body;
        if (!sourceElement) {
          console.error('Browser: No documentElement or body found');
          return null;
        }

        console.log('🌐 Browser: Cloning documentElement...');
        const clonedPage = sourceElement.cloneNode(true);

        if (!clonedPage) {
          console.error('Browser: Cloning returned null');
          return null;
        }

        console.log(
          '🌐 Browser: Document cloned successfully, removing script/link elements...'
        );

        const scriptsAndLinks = clonedPage.querySelectorAll('script, link');
        scriptsAndLinks.forEach((element, index) => {
          try {
            element.remove();
          } catch (removeError) {
            console.warn(
              `Failed to remove element ${index}:`,
              removeError
            );
          }
        });

        const allElements = clonedPage.querySelectorAll('*');

        allElements.forEach((element, index) => {
          try {
            if (
              element.tagName === 'IFRAME' ||
              element.tagName === 'CODE' ||
              element.tagName === 'PRE'
            ) {
              return;
            }

            if (element.tagName !== 'TABLE' && !element.closest('table')) {
              const attrs = Array.from(element.attributes);
              attrs.forEach(attr => {
                try {
                  if (
                    attr.name !== 'class' &&
                    attr.name !== 'id' &&
                    !attr.name.startsWith('data-') &&
                    !attr.name.startsWith('aria-')
                  ) {
                    element.removeAttribute(attr.name);
                  }
                } catch (attrError) {
                  console.warn(
                    `Failed to remove attribute ${attr.name}:`,
                    attrError
                  );
                }
              });
            }
          } catch (elementError) {
            console.warn(
              `Failed to process element ${index}:`,
              elementError
            );
          }
        });

        // Generate the final HTML
        let result;
        if (clonedPage.outerHTML) {
          result = clonedPage.outerHTML;
        } else if (clonedPage.innerHTML) {
          // Fallback: wrap in HTML tags
          result = `<html>${clonedPage.innerHTML}</html>`;
        } else {
          console.error('Browser: No outerHTML or innerHTML available');
          return null;
        }

        console.log(
          '🌐 Browser: HTML generation complete, length:',
          result?.length || 0
        );
        console.log(
          '🌐 Browser: HTML preview:',
          result?.substring(0, 200) || 'no content'
        );
        return result;
      } catch (browserError) {
        console.error('Browser: Error in cloning process:', browserError.message);
        console.error('Browser: Error stack:', browserError.stack?.split('\n').slice(0, 3).join('\n'));

        // Emergency fallback: just get the body content
        try {
          console.log('🌐 Browser: Attempting emergency fallback...');
          const bodyContent = document.body ? document.body.innerHTML : '';
          console.log(
            '🌐 Browser: Emergency fallback length:',
            bodyContent.length
          );
          return bodyContent
            ? `<html><body>${bodyContent}</body></html>`
            : null;
        } catch (fallbackError) {
          console.error('Browser: Emergency fallback also failed:', fallbackError.message);
          console.error('Fallback error details:', fallbackError.stack?.split('\n').slice(0, 3).join('\n'));
          return null;
        }
      }
    });

    streamingService.log(
      `📄 HTML cloning completed, result type: ${typeof clonedHTML}, length: ${clonedHTML?.length || 0}`,
      'info'
    );

    // Additional server-side validation and fallback
    if (
      !clonedHTML ||
      typeof clonedHTML !== 'string' ||
      clonedHTML.trim().length === 0
    ) {
      streamingService.log(
        '⚠️ HTML cloning returned invalid result, attempting server-side fallback...',
        'warn'
      );

      // Fallback method: get the page content directly
      try {
        const pageContent = await context.page.content();
        if (pageContent && pageContent.length > 0) {
          streamingService.log(
            `📄 Server-side fallback successful, length: ${pageContent.length}`,
            'info'
          );
          clonedHTML = pageContent;
        } else {
          streamingService.log(
            '❌ Server-side fallback also returned empty content',
            'error'
          );
          clonedHTML = '';
        }
      } catch (fallbackError) {
        streamingService.log(
          `❌ Server-side fallback failed: ${fallbackError.message}`,
          'error'
        );
        clonedHTML = '';
      }
    }
  } catch (evaluateError) {
    streamingService.log(
      `❌ HTML cloning failed: ${evaluateError.message}`,
      'error'
    );

    // Final fallback: get page content directly
    try {
      streamingService.log(
        '🔄 Attempting final fallback with page.content()...',
        'info'
      );
      clonedHTML = await context.page.content();
      streamingService.log(
        `📄 Final fallback successful, length: ${clonedHTML?.length || 0}`,
        'info'
      );
    } catch (finalError) {
      streamingService.log(
        `❌ Final fallback also failed: ${finalError.message}`,
        'error'
      );
      clonedHTML = '';
    }
  }

  streamingService.updateProgress({
    message: 'Analyzing page structure with AI to find interactive elements',
  });

  let interactiveElements;
  if (
    !clonedHTML ||
    typeof clonedHTML !== 'string' ||
    clonedHTML.trim().length === 0
  ) {
    streamingService.log(
      '⚠️ Invalid or empty HTML content for AI analysis - skipping interactive element detection',
      'warn'
    );
    interactiveElements = { elements: [] };
  } else {
    streamingService.log(
      `📄 Analyzing ${clonedHTML.length} characters of HTML content`,
      'info'
    );
    interactiveElements = await findInteractiveElements(
      clonedHTML,
      userQuery,
      streamingService
    );
  }

  context.performanceMonitor.endPhase();
  streamingService.endPhase({
    success: true,
    elementsFound: interactiveElements.elements?.length || 0,
  });

  // Step 2: Process interactive elements to reveal dynamic content
  streamingService.startPhase(
    'dynamic-content-extraction',
    'Processing interactive elements to reveal hidden content'
  );
  context.performanceMonitor.startPhase('interactive elements processing');

  if (interactiveElements.elements.length > 0) {
    try {
      streamingService.updateProgress({
        message: `Processing ${interactiveElements.elements.length} interactive elements`,
      });

      dynamicContents = await processInteractiveElementsContent(
        context.page,
        interactiveElements
      );

      streamingService.updateProgress({
        message: `Extracted ${dynamicContents.contents?.length || 0} pieces of dynamic content`,
      });
    } catch (error) {
      const handledError = await handleError(error, {
        operation: 'processInteractiveElementsContent',
        url: context.page?.url?.() || 'unknown',
        elementsCount: interactiveElements.elements.length,
        userQuery,
      });

      if (!handledError.shouldRetry) {
        streamingService.log(
          'Continuing without dynamic content extraction...',
          'warn'
        );
        enhancedError = handledError;
      } else {
        throw handledError;
      }
    }
  }

  context.performanceMonitor.endPhase();
  streamingService.endPhase({
    success: true,
    dynamicContentPieces: dynamicContents.contents?.length || 0,
  });

  // Step 3: Combine main content with dynamic content
  streamingService.startPhase(
    'content-processing',
    'Combining main content with dynamic content'
  );
  context.performanceMonitor.startPhase('content processing');

  streamingService.updateProgress({
    message: 'Combining main content with extracted dynamic content',
  });
  const combinedHtml = await combineContent(context.page, dynamicContents);
  await closeBrowser();

  streamingService.updateProgress({
    message: 'Cleaning and formatting HTML content',
  });
  const cleanedHTML = await fixAndFormatHTML(combinedHtml);

  // Step 4: Convert to markdown and improve with AI
  let finalMarkdown;
  if (userQuery) {
    streamingService.updateProgress({
      message:
        'Converting to markdown and improving with AI based on user query',
    });
    finalMarkdown = await convertAndImproveMarkdownFromHTML(
      cleanedHTML,
      userQuery,
      streamingService
    );
  } else {
    streamingService.updateProgress({ message: 'Converting HTML to markdown' });
    finalMarkdown = await convertToMarkdown(cleanedHTML);
  }

  context.performanceMonitor.endPhase();
  streamingService.endPhase({
    success: true,
    markdownLength: finalMarkdown?.length || 0,
    htmlLength: cleanedHTML?.length || 0,
  });

  return {
    enhancedError,
    markdown: finalMarkdown,
    html: cleanedHTML,
  };
}
