#!/usr/bin/env node

/**
 * Test script for the simplified logging system
 */

import { createLogger } from './utils/SimpleLogger.js';
import { createStreamingService } from './utils/StreamingService.js';
import { handleError } from './utils/GlobalErrorHandler.js';

console.log('🧪 Testing Simplified Logging System\n');

// Test 1: Basic Logger
console.log('=== Test 1: Basic Logger ===');
const logger = createLogger();

logger.info('This is an info message');
logger.warn('This is a warning message');
logger.error('This is an error message');

// Test 2: Logger with Phases
console.log('\n=== Test 2: Logger with Phases ===');
logger.startPhase('initialization', 'Setting up test environment');
logger.updateProgress('Loading configuration...');
logger.updateProgress('Connecting to services...');
logger.endPhase({ success: true, itemsProcessed: 42 });

logger.startPhase('processing');
logger.updateProgress('Processing data...');
logger.endPhase();

// Test 3: Error Handling
console.log('\n=== Test 3: Error Handling ===');
try {
  throw new Error('Test network error: connection timeout');
} catch (error) {
  const result = logger.error('Network operation failed', error);
  console.log(`Error classified as: ${result.errorType}, Retryable: ${result.shouldRetry}`);
}

// Test 4: Global Error Handler
console.log('\n=== Test 4: Global Error Handler ===');
try {
  throw new Error('Test authentication failed: invalid API key');
} catch (error) {
  const enhancedError = await handleError(error, {
    operation: 'authenticate',
    service: 'test-api'
  });
  console.log(`Enhanced error: ${enhancedError.userMessage}`);
  console.log(`Should retry: ${enhancedError.shouldRetry}`);
}

// Test 5: Streaming Service (without actual streaming)
console.log('\n=== Test 5: Streaming Service ===');
const streamingService = createStreamingService();

streamingService.startPhase('data-extraction', 'Extracting data from source');
streamingService.updateProgress({ message: 'Processing 50% complete' });
streamingService.log('Custom log message', 'info');
streamingService.endPhase({ recordsProcessed: 100 });

streamingService.complete({ totalRecords: 100, errors: 0 });

// Test 6: Performance
console.log('\n=== Test 6: Performance Test ===');
logger.startPhase('performance-test');
const start = Date.now();

// Simulate some work
for (let i = 0; i < 1000; i++) {
  if (i % 200 === 0) {
    logger.updateProgress(`Processed ${i}/1000 items`);
  }
}

const duration = Date.now() - start;
logger.endPhase({ duration: `${duration}ms`, itemsProcessed: 1000 });

logger.complete('All tests completed successfully!');

console.log('\n✅ Simplified logging system test completed!');
console.log('📊 Stats:', logger.getStats());
