/**
 * Simplified AIProgressLoader - Basic AI operation tracking
 */

import { createLogger } from './SimpleLogger.js';

export class AIProgressLoader {
  constructor(streamingService = null) {
    this.streamingService = streamingService;
    this.currentOperation = null;
    this.startTime = null;
    this.logger = createLogger();
  }

  /**
   * Start an AI operation
   * @param {string} operationType - Type of operation (analyzing, converting, etc.)
   * @param {string} customMessage - Custom message to display
   */
  startOperation(operationType = 'processing', customMessage = null) {
    this.stopOperation(); // Stop any existing operation

    this.currentOperation = {
      type: operationType,
      customMessage,
    };

    this.startTime = Date.now();
    const message = customMessage || `AI ${operationType}`;
    this.log(`🤖 ${message}`);

    // Stream progress if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message,
        aiOperation: {
          type: operationType,
          status: 'started',
        },
      });
    }
  }

  /**
   * Update the progress with a new message
   * @param {string} message - Progress message
   * @param {Object} additionalData - Additional data to include
   */
  updateProgress(message = null, additionalData = {}) {
    if (!this.currentOperation) return;

    const progressMessage = message || `AI ${this.currentOperation.type} in progress...`;
    this.log(`⚡ ${progressMessage}`);

    // Stream progress if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: progressMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'progress',
          ...additionalData,
        },
      });
    }
  }

  /**
   * Complete the current AI operation
   * @param {string} completionMessage - Final completion message
   * @param {Object} result - Operation result data
   */
  completeOperation(completionMessage = null, result = {}) {
    if (!this.currentOperation) return;

    const finalMessage =
      completionMessage ||
      `✅ AI ${this.currentOperation.type} completed successfully!`;

    this.log(finalMessage);

    // Stream completion if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: finalMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'completed',
          result,
        },
      });
    }

    this.currentOperation = null;
  }

  /**
   * Handle AI operation error
   * @param {Error} error - Error that occurred
   * @param {string} errorMessage - Custom error message
   */
  errorOperation(error, errorMessage = null) {
    if (!this.currentOperation) return;

    const finalMessage =
      errorMessage ||
      `❌ AI ${this.currentOperation.type} failed: ${error.message}`;

    this.log(finalMessage, 'error');

    // Stream error if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: finalMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'error',
          error: {
            message: error.message,
            code: error.code || 'AI_OPERATION_ERROR',
          },
        },
      });
    }

    this.currentOperation = null;
  }

  /**
   * Stop the current operation
   */
  stopOperation() {
    this.currentOperation = null;
  }

  /**
   * Log a message (with optional level)
   * @param {string} message - Message to log
   * @param {string} level - Log level
   */
  log(message, level = 'info') {
    if (this.streamingService) {
      this.streamingService.log(message, level);
    } else {
      this.logger.log(message, level);
    }
  }

  /**
   * Create a simple progress callback for AI streaming operations
   * @param {string} operationType - Type of AI operation
   * @returns {Function} Progress callback function
   */
  createProgressCallback(operationType = 'processing') {
    let chunkCount = 0;

    return {
      onStart: (customMessage = null) => {
        this.startOperation(operationType, customMessage);
      },

      onProgress: () => {
        chunkCount++;
        // Update every 100 chunks to avoid spam
        if (chunkCount % 100 === 0) {
          this.updateProgress(`Processed ${chunkCount} chunks`);
        }
      },

      onComplete: (result = {}) => {
        this.completeOperation(`Completed processing ${chunkCount} chunks`, result);
      },

      onError: error => {
        this.errorOperation(error);
      },
    };
  }

  /**
   * Get operation statistics
   * @returns {Object} Current operation stats
   */
  getStats() {
    if (!this.currentOperation) {
      return { active: false };
    }

    return {
      active: true,
      type: this.currentOperation.type,
      elapsed: Date.now() - this.startTime,
    };
  }
}

/**
 * Create a new AI Progress Loader instance
 * @param {Object} streamingService - Optional streaming service instance
 * @returns {AIProgressLoader} New loader instance
 */
export function createAIProgressLoader(streamingService = null) {
  return new AIProgressLoader(streamingService);
}

/**
 * Global AI progress loader for simple usage
 */
export const globalAILoader = new AIProgressLoader();
